using Furion.EventBus;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;

namespace IotPlatform.Application.Middleware;

/// <summary>
/// 请求拦截中间件（备选方案）
/// </summary>
public class RequestInterceptorMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ISqlSugarRepository<OpenApiEntity> _openApiEntity;

    public RequestInterceptorMiddleware(RequestDelegate next, ISqlSugarRepository<OpenApiEntity> openApiEntity)
    {
        _next = next;
        _openApiEntity = openApiEntity;
        Console.WriteLine("[RequestInterceptorMiddleware] 中间件已创建");
    }

    public async Task InvokeAsync(HttpContext context)
    {
        string path = context.Request.Path.Value;
        Console.WriteLine($"[RequestInterceptorMiddleware] 中间件被触发 - 路径: {path}");

        // 只处理特定路径
        if (path.StartsWith("/webhook") || path.StartsWith("/script/otherGetAction") || path.StartsWith("/openapi"))
        {
            Console.WriteLine($"[RequestInterceptorMiddleware] 处理路径: {path}");
            
            if (path.StartsWith("/webhook"))
            {
                await MessageCenter.PublishAsync(path, path);
            }
            else if (path.StartsWith("/script/otherGetAction"))
            {
                // 处理脚本请求
            }
            else if (path.StartsWith("/openapi"))
            {
                try
                {
                    long appId = 0;

                    // 首先尝试从路由路径中提取AppId
                    appId = TryExtractAppIdFromRoute(path);

                    // 如果路由中没有AppId，则按原有逻辑从其他地方获取
                    if (appId <= 0)
                    {
                        if (context.Request.Method == "POST")
                        {
                            string body = await context.Request.ReadBodyContentAsync();
                            JObject jsonObject = JObject.Parse(body);
                            appId = (long)jsonObject["AppId"];
                        }
                        else if (context.Request.Method == "GET")
                        {
                            if (context.Request.Query.ContainsKey("AppId"))
                            {
                                appId = Convert.ToInt64(context.Request.Query["AppId"]);
                            }
                        }
                    }

                    if (appId <= 0)
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("AppId is required");
                        return;
                    }

                    // 请求头中获取token
                    string token = context.Request.Headers.Authorization;
                    
                    // 在此处进行token的有效性判断
                    if (!await IsTokenValid(token, path, appId))
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("Unauthorized");
                        return;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[RequestInterceptorMiddleware] 异常: {ex.Message}");
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsync("Authentication failed");
                    return;
                }
            }
        }

        await _next(context);
    }

    /// <summary>
    /// 尝试从路由路径中提取AppId
    /// </summary>
    /// <param name="route">路由路径</param>
    /// <returns>AppId，如果提取失败返回0</returns>
    private long TryExtractAppIdFromRoute(string route)
    {
        try
        {
            var segments = route.Split('/', StringSplitOptions.RemoveEmptyEntries);

            if (segments.Length >= 2 && segments[0].Equals("openapi", StringComparison.OrdinalIgnoreCase))
            {
                if (long.TryParse(segments[1], out long appId) && appId > 0)
                {
                    return appId;
                }
            }
        }
        catch
        {
            // 解析失败，返回0
        }

        return 0;
    }

    /// <summary>
    /// 校验是否有效
    /// </summary>
    /// <param name="token"></param>
    /// <param name="path"></param>
    /// <param name="appId"></param>
    /// <returns></returns>
    private async Task<bool> IsTokenValid(string token, string path, long appId)
    {
        // 无认证
        if (!token.IsNullOrEmpty())
        {
            token = token.Replace("Bearer ", "").Trim();
            (bool isValid, _, _) = JWTEncryption.Validate(token);
            if (isValid)
            {
                return true;
            }
        }

        OpenApiEntity openApi = await _openApiEntity.AsQueryable()
            .Where(w => w.Id == appId)
            .Where(w => w.AuthorizeType == AuthorizeTypeEnum.No)
            .Includes(w => w.OpenApiDetailEntity.Where(x => x.Path == path).ToList())
            .FirstAsync();
        return openApi != null && openApi.OpenApiDetailEntity.Any();
    }
}
